/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-orange: #FF6B35;
    --secondary-orange: #FF8C42;
    --primary-blue: #1E3A8A;
    --secondary-blue: #3B82F6;
    --dark-blue: #1E40AF;
    --light-blue: #DBEAFE;
    --white: #FFFFFF;
    --black: #000000;
    --gray-light: #F8FAFC;
    --gray-medium: #64748B;
    --gray-dark: #334155;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--gray-dark);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    height: 40px;
    width: auto;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-orange);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-dark);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-orange);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-orange);
    transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-orange);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    overflow: hidden;
}

.parallax-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 120%;
    height: 120%;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, var(--primary-orange) 100%);
    background-size: cover;
    background-position: center;
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-orange);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--secondary-orange);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-blue);
    transform: translateY(-2px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid var(--white);
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) rotate(-45deg) translateY(0);
    }
    40% {
        transform: translateX(-50%) rotate(-45deg) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) rotate(-45deg) translateY(-5px);
    }
}

/* Section Styles */
section {
    padding: 100px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 15px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--gray-medium);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h3 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.about-text p {
    margin-bottom: 25px;
    line-height: 1.8;
}

.about-features {
    list-style: none;
}

.about-features li {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.about-features i {
    color: var(--primary-orange);
    font-size: 1.2rem;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: var(--shadow);
}

/* Product Showcase Component */
.product-showcase {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    overflow: hidden;
}

/* Full-screen gradient background (static) */
.showcase-gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF69B4 0%, #E6E6FA 100%);
    z-index: 1;
}

/* Fixed white content card */
.showcase-card {
    position: relative;
    z-index: 2;
    background: var(--white);
    max-width: 600px;
    width: 90%;
    height: 400px;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Product Navigation */
.showcase-nav {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 0;
}

.nav-btn {
    flex: 1;
    padding: 15px 10px;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    color: var(--gray-medium);
    font-weight: 500;
}

.nav-btn i {
    font-size: 1.2rem;
    margin-bottom: 2px;
}

.nav-btn.active {
    background: var(--white);
    color: var(--primary-orange);
    border-bottom: 3px solid var(--primary-orange);
}

.nav-btn:hover:not(.active) {
    background: #f1f5f9;
    color: var(--primary-blue);
}

/* Dynamic Content Area */
.showcase-content {
    flex: 1;
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.content-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 30px;
    display: flex;
    align-items: center;
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-slide.active {
    opacity: 1;
    transform: translateX(0);
    position: relative;
}

.content-slide.exit {
    opacity: 0;
    transform: translateX(-30px);
}

.content-left {
    flex: 1;
    padding-right: 20px;
}

.content-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.content-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 15px;
}

.content-description {
    color: var(--gray-medium);
    line-height: 1.6;
    margin-bottom: 20px;
}

.content-features {
    list-style: none;
    margin-bottom: 20px;
}

.content-features li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    color: var(--gray-dark);
    font-size: 0.9rem;
}

.content-features i {
    color: var(--primary-orange);
    font-size: 0.8rem;
}

.content-image {
    width: 150px;
    height: 150px;
    border-radius: 15px;
    object-fit: cover;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.content-image:hover {
    transform: scale(1.05);
}

.content-chart {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(
        var(--primary-orange) 0deg 120deg,
        var(--secondary-orange) 120deg 240deg,
        var(--primary-blue) 240deg 360deg
    );
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: rotate 10s linear infinite;
}

.content-chart::before {
    content: '';
    width: 80px;
    height: 80px;
    background: var(--white);
    border-radius: 50%;
    position: absolute;
}

.chart-text {
    position: relative;
    z-index: 1;
    font-weight: 700;
    color: var(--primary-blue);
    text-align: center;
    font-size: 0.8rem;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* WhatsApp CTA */
.showcase-cta {
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
}

.showcase-whatsapp-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    padding: 12px 20px;
    background: #25D366;
    color: var(--white);
    text-decoration: none;
    border-radius: 10px;
    font-weight: 600;
    transition: var(--transition);
}

.showcase-whatsapp-btn:hover {
    background: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
}

/* Navigation Dots */
.showcase-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
}

.dot.active {
    background: var(--white);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Services Section */
.services-section {
    position: relative;
    color: var(--white);
}

.services-section .parallax-bg {
    background: linear-gradient(45deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
}

.services-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 58, 138, 0.8);
    z-index: 1;
}

.services-section .container {
    position: relative;
    z-index: 2;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.service-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.service-card p {
    margin-bottom: 25px;
    opacity: 0.9;
}

.service-whatsapp {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-orange);
    text-decoration: none;
    font-weight: 600;
    padding: 10px 20px;
    border: 2px solid var(--primary-orange);
    border-radius: 25px;
    transition: var(--transition);
}

.service-whatsapp:hover {
    background: var(--primary-orange);
    color: var(--white);
}

/* Testimonials Section */
.testimonials-section {
    background: var(--gray-light);
}

.testimonials-slider {
    max-width: 800px;
    margin: 0 auto;
}

.testimonial-item {
    background: var(--white);
    padding: 40px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    text-align: center;
    margin: 0 15px;
}

.testimonial-text {
    font-size: 1.1rem;
    font-style: italic;
    margin-bottom: 25px;
    line-height: 1.8;
}

.testimonial-author {
    font-weight: 600;
    color: var(--primary-blue);
}

.testimonial-company {
    color: var(--gray-medium);
    font-size: 0.9rem;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: var(--primary-orange);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.contact-item h4 {
    color: var(--primary-blue);
    margin-bottom: 5px;
}

.contact-form {
    background: var(--gray-light);
    padding: 40px;
    border-radius: 15px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #E2E8F0;
    border-radius: 10px;
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-orange);
}

/* Footer */
.footer {
    background: var(--primary-blue);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.footer-logo img {
    height: 40px;
}

.footer-logo span {
    font-size: 1.5rem;
    font-weight: 700;
}

.footer-section h4 {
    margin-bottom: 20px;
    color: var(--primary-orange);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-orange);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-orange);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--secondary-orange);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0.8;
}

/* Floating WhatsApp Button */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.whatsapp-float a {
    width: 60px;
    height: 60px;
    background: #25D366;
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: var(--shadow);
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.whatsapp-float a:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-hover);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-orange);
    color: var(--white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    z-index: 999;
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: var(--secondary-orange);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: var(--white);
        width: 100%;
        text-align: center;
        transition: var(--transition);
        box-shadow: var(--shadow);
        padding: 20px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    /* Product Showcase Mobile */
    .showcase-card {
        width: 95%;
        height: auto;
        min-height: 500px;
        max-width: none;
    }

    .showcase-nav {
        flex-wrap: wrap;
    }

    .nav-btn {
        flex: 1 1 50%;
        min-width: 120px;
        padding: 12px 8px;
        font-size: 0.8rem;
    }

    .nav-btn i {
        font-size: 1rem;
    }

    .content-slide {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }

    .content-left {
        padding-right: 0;
        margin-bottom: 20px;
    }

    .content-title {
        font-size: 1.5rem;
    }

    .content-image {
        width: 120px;
        height: 120px;
    }

    .content-chart {
        width: 100px;
        height: 100px;
    }

    .content-chart::before {
        width: 70px;
        height: 70px;
    }

    .section-title {
        font-size: 2rem;
    }

    section {
        padding: 60px 0;
    }

    .whatsapp-float {
        bottom: 20px;
        right: 20px;
    }

    .whatsapp-float a {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .btn {
        padding: 12px 25px;
        font-size: 0.9rem;
    }

    .service-card,
    .contact-form {
        padding: 30px 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading animation */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}
