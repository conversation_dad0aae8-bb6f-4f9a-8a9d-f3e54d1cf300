// Configuration file for DopeHub Creative Solution website
// Update these values with your actual business information

const CONFIG = {
    // WhatsApp Business Number (include country code, no + sign)
    // Example: for ****** 567 8900, use: ***********
    WHATSAPP_NUMBER: 'YOUR_WHATSAPP_NUMBER',
    
    // Business Information
    BUSINESS: {
        name: 'DopeHub Creative Solution',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: 'Your Business Address, City, State, ZIP',
        
        // Social Media Links
        social: {
            whatsapp: 'https://wa.me/YOUR_WHATSAPP_NUMBER',
            instagram: 'https://instagram.com/dopehubcreative',
            tiktok: 'https://tiktok.com/@dopehubcreative',
            email: 'mailto:<EMAIL>'
        }
    },
    
    // Default WhatsApp Messages
    MESSAGES: {
        general: 'Hi! I\'m interested in your printing services',
        dtf: 'Hi! I\'m interested in DTF printing services',
        embroidery: 'Hi! I\'m interested in embroidery services',
        tags: 'Hi! I\'m interested in tags and stickers',
        custom: 'Hi! I\'m interested in custom printing services'
    },
    
    // Website Settings
    SETTINGS: {
        enableTypingEffect: true,
        enableParallax: true,
        enableAnimations: true,
        animationDuration: 1000
    }
};

// Helper function to generate WhatsApp URL
function getWhatsAppURL(message = CONFIG.MESSAGES.general) {
    return `https://wa.me/${CONFIG.WHATSAPP_NUMBER}?text=${encodeURIComponent(message)}`;
}

// Helper function to update WhatsApp links
function updateWhatsAppLinks() {
    // Update all WhatsApp links on the page
    const whatsappLinks = document.querySelectorAll('a[href*="YOUR_WHATSAPP_NUMBER"]');
    whatsappLinks.forEach(link => {
        const href = link.getAttribute('href');
        const updatedHref = href.replace('YOUR_WHATSAPP_NUMBER', CONFIG.WHATSAPP_NUMBER);
        link.setAttribute('href', updatedHref);
    });
}

// Initialize configuration when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (CONFIG.WHATSAPP_NUMBER !== 'YOUR_WHATSAPP_NUMBER') {
        updateWhatsAppLinks();
    } else {
        console.warn('⚠️ Please update your WhatsApp number in config.js');
    }
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
