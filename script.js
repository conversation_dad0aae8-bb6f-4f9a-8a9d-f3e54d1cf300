// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// Initialize Rellax (Parallax)
var rellax = new Rellax('.parallax-bg');

// DOM Elements
const navbar = document.getElementById('navbar');
const mobileMenu = document.getElementById('mobile-menu');
const navMenu = document.getElementById('nav-menu');
const scrollToTopBtn = document.getElementById('scroll-to-top');
const contactForm = document.getElementById('contact-form');

// Mobile Navigation Toggle
mobileMenu.addEventListener('click', () => {
    mobileMenu.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on nav links
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        mobileMenu.classList.remove('active');
        navMenu.classList.remove('active');
    });
});

// Navbar scroll effect
window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
        navbar.classList.add('scrolled');
        scrollToTopBtn.classList.add('show');
    } else {
        navbar.classList.remove('scrolled');
        scrollToTopBtn.classList.remove('show');
    }
});

// Scroll to top functionality
scrollToTopBtn.addEventListener('click', () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// Active navigation link highlighting
const sections = document.querySelectorAll('section');
const navLinks = document.querySelectorAll('.nav-link');

window.addEventListener('scroll', () => {
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Product Showcase Data
const showcaseData = {
    dtf: {
        title: 'DTF Prints',
        description: 'Direct-to-Film printing technology delivers vibrant, durable designs on various fabrics with exceptional detail and color accuracy.',
        features: [
            'Vibrant color reproduction',
            'Durable and wash-resistant',
            'Works on multiple fabric types',
            'High-resolution detail',
            'Quick turnaround time'
        ],
        image: 'assets/gallery/dtf-1.jpg',
        whatsappMessage: 'Hi! I\'m interested in DTF printing services. Can you provide more details?',
        stats: {
            quality: '99%',
            durability: '95%',
            versatility: '90%'
        }
    },
    embroidery: {
        title: 'Embroidery Services',
        description: 'Professional embroidery services for logos, designs, and custom patterns with precision stitching and premium threads.',
        features: [
            'Premium thread quality',
            'Precision stitching',
            'Logo digitization included',
            'Multiple fabric compatibility',
            'Corporate branding solutions'
        ],
        image: 'assets/gallery/embroidery-1.jpg',
        whatsappMessage: 'Hi! I\'m interested in embroidery services. What are your capabilities?',
        stats: {
            precision: '98%',
            durability: '100%',
            detail: '95%'
        }
    },
    tags: {
        title: 'Tags & Stickers',
        description: 'Custom tags, labels, and stickers for branding, packaging, and promotional purposes with various materials and finishes.',
        features: [
            'Weather-resistant materials',
            'Custom shapes and sizes',
            'Adhesive options available',
            'Bulk quantity discounts',
            'Fast production times'
        ],
        image: 'assets/gallery/tags-1.jpg',
        whatsappMessage: 'Hi! I\'m interested in custom tags and stickers. What options do you have?',
        stats: {
            durability: '92%',
            customization: '100%',
            speed: '88%'
        }
    },
    custom: {
        title: 'Custom Printing',
        description: 'Comprehensive printing solutions including flyers, brochures, business cards, and specialized marketing materials.',
        features: [
            'Multiple paper types',
            'Professional finishing',
            'Design consultation',
            'Bulk order discounts',
            'Same-day rush service'
        ],
        image: 'assets/gallery/others-1.jpg',
        whatsappMessage: 'Hi! I\'m interested in custom printing services. What can you help me with?',
        stats: {
            variety: '100%',
            quality: '96%',
            service: '94%'
        }
    }
};

// Testimonials Data
const testimonialsData = [
    {
        text: "DopeHub Creative Solution exceeded our expectations! The quality of their DTF prints is outstanding, and the customer service is top-notch. Highly recommended!",
        author: "Sarah Johnson",
        company: "Fashion Boutique Owner"
    },
    {
        text: "We've been working with DopeHub for our corporate embroidery needs. Their attention to detail and quick turnaround times make them our go-to printing partner.",
        author: "Michael Chen",
        company: "Tech Startup CEO"
    },
    {
        text: "The custom stickers and tags they created for our product line were perfect. Great quality, competitive prices, and excellent communication throughout the process.",
        author: "Emma Rodriguez",
        company: "Small Business Owner"
    }
];

// Product Showcase Functionality
let currentProduct = 'dtf';

// DOM Elements for Showcase
const showcaseContent = document.getElementById('showcase-content');
const showcaseWhatsApp = document.getElementById('showcase-whatsapp');
const navBtns = document.querySelectorAll('.nav-btn');
const dots = document.querySelectorAll('.dot');

// Render Product Content
function renderProductContent(productKey) {
    const product = showcaseData[productKey];
    if (!product) return;

    const contentHTML = `
        <div class="content-slide active">
            <div class="content-left">
                <h3 class="content-title">${product.title}</h3>
                <p class="content-description">${product.description}</p>
                <ul class="content-features">
                    ${product.features.map(feature => `
                        <li><i class="fas fa-check-circle"></i> ${feature}</li>
                    `).join('')}
                </ul>
            </div>
            <div class="content-right">
                <div class="content-visual">
                    <img src="${product.image}" alt="${product.title}" class="content-image"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="content-chart" style="display: none;">
                        <div class="chart-text">
                            Quality<br>
                            <strong>${product.stats.quality || '95%'}</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Fade out current content
    const currentSlide = showcaseContent.querySelector('.content-slide');
    if (currentSlide) {
        currentSlide.classList.add('exit');
        setTimeout(() => {
            showcaseContent.innerHTML = contentHTML;
            // Update WhatsApp link
            updateWhatsAppLink(product.whatsappMessage);
        }, 250);
    } else {
        showcaseContent.innerHTML = contentHTML;
        updateWhatsAppLink(product.whatsappMessage);
    }
}

// Update WhatsApp Link
function updateWhatsAppLink(message) {
    const whatsappNumber = CONFIG?.WHATSAPP_NUMBER || 'YOUR_WHATSAPP_NUMBER';
    const encodedMessage = encodeURIComponent(message);
    showcaseWhatsApp.href = `https://wa.me/${whatsappNumber}?text=${encodedMessage}`;
}

// Switch Product
function switchProduct(productKey) {
    if (productKey === currentProduct) return;

    currentProduct = productKey;

    // Update navigation buttons
    navBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-product') === productKey) {
            btn.classList.add('active');
        }
    });

    // Update dots
    dots.forEach(dot => {
        dot.classList.remove('active');
        if (dot.getAttribute('data-product') === productKey) {
            dot.classList.add('active');
        }
    });

    // Render new content
    renderProductContent(productKey);
}

// Event Listeners for Navigation
navBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        const productKey = btn.getAttribute('data-product');
        switchProduct(productKey);
    });
});

// Event Listeners for Dots
dots.forEach(dot => {
    dot.addEventListener('click', () => {
        const productKey = dot.getAttribute('data-product');
        switchProduct(productKey);
    });
});

// Auto-rotate products (optional)
let autoRotateInterval;

function startAutoRotate() {
    const products = Object.keys(showcaseData);
    let currentIndex = products.indexOf(currentProduct);

    autoRotateInterval = setInterval(() => {
        currentIndex = (currentIndex + 1) % products.length;
        switchProduct(products[currentIndex]);
    }, 8000); // Change every 8 seconds
}

function stopAutoRotate() {
    if (autoRotateInterval) {
        clearInterval(autoRotateInterval);
        autoRotateInterval = null;
    }
}

// Pause auto-rotate on user interaction
document.querySelector('.product-showcase').addEventListener('mouseenter', stopAutoRotate);
document.querySelector('.product-showcase').addEventListener('mouseleave', startAutoRotate);

// Render Testimonials
function renderTestimonials() {
    const testimonialsSlider = document.querySelector('.testimonials-slider');
    testimonialsSlider.innerHTML = '';
    
    testimonialsData.forEach((testimonial, index) => {
        const testimonialItem = document.createElement('div');
        testimonialItem.className = 'testimonial-item';
        testimonialItem.setAttribute('data-aos', 'fade-up');
        testimonialItem.setAttribute('data-aos-delay', index * 200);
        
        testimonialItem.innerHTML = `
            <div class="testimonial-text">"${testimonial.text}"</div>
            <div class="testimonial-author">${testimonial.author}</div>
            <div class="testimonial-company">${testimonial.company}</div>
        `;
        
        testimonialsSlider.appendChild(testimonialItem);
    });
}

// Contact Form Handler
contactForm.addEventListener('submit', (e) => {
    e.preventDefault();
    
    const formData = new FormData(contactForm);
    const name = formData.get('name');
    const service = formData.get('service');
    const quantity = formData.get('quantity');
    const message = formData.get('message');
    
    // Create WhatsApp message
    let whatsappMessage = `Hi! I'm ${name} and I'm interested in your services.\n\n`;
    whatsappMessage += `Service Type: ${service}\n`;
    if (quantity) {
        whatsappMessage += `Quantity: ${quantity}\n`;
    }
    whatsappMessage += `Message: ${message}`;
    
    // Encode message for URL
    const encodedMessage = encodeURIComponent(whatsappMessage);
    
    // Open WhatsApp
    window.open(`https://wa.me/YOUR_WHATSAPP_NUMBER?text=${encodedMessage}`, '_blank');
    
    // Reset form
    contactForm.reset();
    
    // Show success message (optional)
    alert('Redirecting to WhatsApp...');
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Initialize showcase and testimonials on page load
document.addEventListener('DOMContentLoaded', () => {
    // Initialize product showcase
    if (showcaseContent) {
        renderProductContent('dtf');
        startAutoRotate();
    }

    renderTestimonials();

    // Add loading animation to elements
    const elements = document.querySelectorAll('.loading');
    elements.forEach(el => {
        setTimeout(() => {
            el.classList.add('loaded');
        }, 100);
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate');
        }
    });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.service-card, .gallery-item, .testimonial-item').forEach(el => {
    observer.observe(el);
});

// Parallax effect for hero section on scroll
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallaxBg = document.querySelector('.hero .parallax-bg');
    if (parallaxBg) {
        parallaxBg.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
});

// Typing effect for hero title (optional enhancement)
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Initialize typing effect on page load
window.addEventListener('load', () => {
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        const originalText = heroTitle.textContent;
        typeWriter(heroTitle, originalText, 80);
    }
});

// WhatsApp floating button pulse effect
const whatsappFloat = document.getElementById('whatsapp-float');
if (whatsappFloat) {
    setInterval(() => {
        whatsappFloat.style.animation = 'none';
        setTimeout(() => {
            whatsappFloat.style.animation = 'pulse 2s infinite';
        }, 10);
    }, 10000); // Pulse every 10 seconds
}
