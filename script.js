// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// Initialize Rellax (Parallax)
var rellax = new Rellax('.parallax-bg');

// DOM Elements
const navbar = document.getElementById('navbar');
const mobileMenu = document.getElementById('mobile-menu');
const navMenu = document.getElementById('nav-menu');
const scrollToTopBtn = document.getElementById('scroll-to-top');
const contactForm = document.getElementById('contact-form');
const galleryGrid = document.getElementById('gallery-grid');
const filterBtns = document.querySelectorAll('.filter-btn');

// Mobile Navigation Toggle
mobileMenu.addEventListener('click', () => {
    mobileMenu.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on nav links
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        mobileMenu.classList.remove('active');
        navMenu.classList.remove('active');
    });
});

// Navbar scroll effect
window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
        navbar.classList.add('scrolled');
        scrollToTopBtn.classList.add('show');
    } else {
        navbar.classList.remove('scrolled');
        scrollToTopBtn.classList.remove('show');
    }
});

// Scroll to top functionality
scrollToTopBtn.addEventListener('click', () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// Active navigation link highlighting
const sections = document.querySelectorAll('section');
const navLinks = document.querySelectorAll('.nav-link');

window.addEventListener('scroll', () => {
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Gallery Data
const galleryData = [
    {
        id: 1,
        category: 'dtf',
        title: 'Custom T-Shirt Design',
        description: 'High-quality DTF print on premium cotton',
        image: 'assets/gallery/dtf-1.jpg'
    },
    {
        id: 2,
        category: 'dtf',
        title: 'Logo Print',
        description: 'Professional logo printing with vibrant colors',
        image: 'assets/gallery/dtf-2.jpg'
    },
    {
        id: 3,
        category: 'embroidery',
        title: 'Corporate Embroidery',
        description: 'Elegant embroidered company logo',
        image: 'assets/gallery/embroidery-1.jpg'
    },
    {
        id: 4,
        category: 'embroidery',
        title: 'Custom Cap Design',
        description: 'Personalized embroidered caps',
        image: 'assets/gallery/embroidery-2.jpg'
    },
    {
        id: 5,
        category: 'tags',
        title: 'Product Labels',
        description: 'Custom product tags and labels',
        image: 'assets/gallery/tags-1.jpg'
    },
    {
        id: 6,
        category: 'tags',
        title: 'Brand Stickers',
        description: 'High-quality vinyl stickers',
        image: 'assets/gallery/tags-2.jpg'
    },
    {
        id: 7,
        category: 'others',
        title: 'Business Flyers',
        description: 'Professional marketing materials',
        image: 'assets/gallery/others-1.jpg'
    },
    {
        id: 8,
        category: 'others',
        title: 'Business Cards',
        description: 'Premium business card printing',
        image: 'assets/gallery/others-2.jpg'
    }
];

// Testimonials Data
const testimonialsData = [
    {
        text: "DopeHub Creative Solution exceeded our expectations! The quality of their DTF prints is outstanding, and the customer service is top-notch. Highly recommended!",
        author: "Sarah Johnson",
        company: "Fashion Boutique Owner"
    },
    {
        text: "We've been working with DopeHub for our corporate embroidery needs. Their attention to detail and quick turnaround times make them our go-to printing partner.",
        author: "Michael Chen",
        company: "Tech Startup CEO"
    },
    {
        text: "The custom stickers and tags they created for our product line were perfect. Great quality, competitive prices, and excellent communication throughout the process.",
        author: "Emma Rodriguez",
        company: "Small Business Owner"
    }
];

// Render Gallery Items
function renderGallery(items = galleryData) {
    galleryGrid.innerHTML = '';
    items.forEach(item => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item';
        galleryItem.setAttribute('data-aos', 'fade-up');
        
        galleryItem.innerHTML = `
            <img src="${item.image}" alt="${item.title}" onerror="this.src='https://via.placeholder.com/300x250/FF6B35/FFFFFF?text=${item.title}'">
            <div class="gallery-item-content">
                <h4>${item.title}</h4>
                <p>${item.description}</p>
                <a href="https://wa.me/YOUR_WHATSAPP_NUMBER?text=Hi! I'm interested in ${item.title}" class="gallery-whatsapp" target="_blank">
                    <i class="fab fa-whatsapp"></i> Inquire about this
                </a>
            </div>
        `;
        
        galleryGrid.appendChild(galleryItem);
    });
    
    // Refresh AOS for new elements
    AOS.refresh();
}

// Gallery Filter Functionality
filterBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        // Remove active class from all buttons
        filterBtns.forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        btn.classList.add('active');
        
        const filter = btn.getAttribute('data-filter');
        
        if (filter === 'all') {
            renderGallery(galleryData);
        } else {
            const filteredItems = galleryData.filter(item => item.category === filter);
            renderGallery(filteredItems);
        }
    });
});

// Render Testimonials
function renderTestimonials() {
    const testimonialsSlider = document.querySelector('.testimonials-slider');
    testimonialsSlider.innerHTML = '';
    
    testimonialsData.forEach((testimonial, index) => {
        const testimonialItem = document.createElement('div');
        testimonialItem.className = 'testimonial-item';
        testimonialItem.setAttribute('data-aos', 'fade-up');
        testimonialItem.setAttribute('data-aos-delay', index * 200);
        
        testimonialItem.innerHTML = `
            <div class="testimonial-text">"${testimonial.text}"</div>
            <div class="testimonial-author">${testimonial.author}</div>
            <div class="testimonial-company">${testimonial.company}</div>
        `;
        
        testimonialsSlider.appendChild(testimonialItem);
    });
}

// Contact Form Handler
contactForm.addEventListener('submit', (e) => {
    e.preventDefault();
    
    const formData = new FormData(contactForm);
    const name = formData.get('name');
    const service = formData.get('service');
    const quantity = formData.get('quantity');
    const message = formData.get('message');
    
    // Create WhatsApp message
    let whatsappMessage = `Hi! I'm ${name} and I'm interested in your services.\n\n`;
    whatsappMessage += `Service Type: ${service}\n`;
    if (quantity) {
        whatsappMessage += `Quantity: ${quantity}\n`;
    }
    whatsappMessage += `Message: ${message}`;
    
    // Encode message for URL
    const encodedMessage = encodeURIComponent(whatsappMessage);
    
    // Open WhatsApp
    window.open(`https://wa.me/YOUR_WHATSAPP_NUMBER?text=${encodedMessage}`, '_blank');
    
    // Reset form
    contactForm.reset();
    
    // Show success message (optional)
    alert('Redirecting to WhatsApp...');
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Initialize gallery and testimonials on page load
document.addEventListener('DOMContentLoaded', () => {
    renderGallery();
    renderTestimonials();
    
    // Add loading animation to elements
    const elements = document.querySelectorAll('.loading');
    elements.forEach(el => {
        setTimeout(() => {
            el.classList.add('loaded');
        }, 100);
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate');
        }
    });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.service-card, .gallery-item, .testimonial-item').forEach(el => {
    observer.observe(el);
});

// Parallax effect for hero section on scroll
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallaxBg = document.querySelector('.hero .parallax-bg');
    if (parallaxBg) {
        parallaxBg.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
});

// Typing effect for hero title (optional enhancement)
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Initialize typing effect on page load
window.addEventListener('load', () => {
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        const originalText = heroTitle.textContent;
        typeWriter(heroTitle, originalText, 80);
    }
});

// WhatsApp floating button pulse effect
const whatsappFloat = document.getElementById('whatsapp-float');
if (whatsappFloat) {
    setInterval(() => {
        whatsappFloat.style.animation = 'none';
        setTimeout(() => {
            whatsappFloat.style.animation = 'pulse 2s infinite';
        }, 10);
    }, 10000); // Pulse every 10 seconds
}
