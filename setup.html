<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DopeHub Website Setup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #1E3A8A 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .setup-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .setup-header h1 {
            color: #1E3A8A;
            margin-bottom: 10px;
        }
        
        .setup-header p {
            color: #64748B;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #1E3A8A;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #E2E8F0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #FF6B35;
        }
        
        .btn {
            background: #FF6B35;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s ease;
        }
        
        .btn:hover {
            background: #FF8C42;
        }
        
        .instructions {
            background: #F8FAFC;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #FF6B35;
        }
        
        .instructions h3 {
            color: #1E3A8A;
            margin-bottom: 10px;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
            color: #64748B;
        }
        
        .success-message {
            background: #10B981;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }
        
        .warning {
            background: #FEF3C7;
            color: #92400E;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #F59E0B;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>🚀 DopeHub Website Setup</h1>
            <p>Configure your website with your business information</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This setup page is for initial configuration only. Delete this file after setup is complete.
        </div>
        
        <div class="instructions">
            <h3>📋 Setup Instructions</h3>
            <ol>
                <li>Fill in your business information below</li>
                <li>Click "Generate Configuration" to create your config</li>
                <li>Copy the generated code and paste it into <code>config.js</code></li>
                <li>Add your logo and images to the <code>assets/</code> folder</li>
                <li>Open <code>index.html</code> in your browser to view your website</li>
                <li>Delete this <code>setup.html</code> file when done</li>
            </ol>
        </div>
        
        <form id="setup-form">
            <div class="form-group">
                <label for="whatsapp">WhatsApp Business Number (with country code, no + sign):</label>
                <input type="text" id="whatsapp" placeholder="e.g., 1234567890" required>
            </div>
            
            <div class="form-group">
                <label for="business-name">Business Name:</label>
                <input type="text" id="business-name" value="DopeHub Creative Solution" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number:</label>
                <input type="text" id="phone" placeholder="+****************" required>
            </div>
            
            <div class="form-group">
                <label for="address">Business Address:</label>
                <textarea id="address" rows="3" placeholder="123 Business St, City, State, ZIP" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="instagram">Instagram Username (optional):</label>
                <input type="text" id="instagram" placeholder="dopehubcreative">
            </div>
            
            <div class="form-group">
                <label for="tiktok">TikTok Username (optional):</label>
                <input type="text" id="tiktok" placeholder="dopehubcreative">
            </div>
            
            <button type="submit" class="btn">🔧 Generate Configuration</button>
        </form>
        
        <div class="success-message" id="success-message">
            <strong>✅ Configuration Generated!</strong> Copy the code below and paste it into your <code>config.js</code> file.
        </div>
        
        <div id="generated-config" style="display: none; margin-top: 20px;">
            <textarea id="config-output" rows="20" style="width: 100%; font-family: monospace; font-size: 12px;" readonly></textarea>
            <button type="button" onclick="copyConfig()" class="btn" style="margin-top: 10px;">📋 Copy Configuration</button>
        </div>
    </div>
    
    <script>
        document.getElementById('setup-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const config = {
                whatsapp: formData.get('whatsapp') || 'YOUR_WHATSAPP_NUMBER',
                businessName: formData.get('business-name') || 'DopeHub Creative Solution',
                email: formData.get('email') || '<EMAIL>',
                phone: formData.get('phone') || '+1234567890',
                address: formData.get('address') || 'Your Business Address',
                instagram: formData.get('instagram') || 'dopehubcreative',
                tiktok: formData.get('tiktok') || 'dopehubcreative'
            };
            
            const configCode = `// Configuration file for DopeHub Creative Solution website
// Generated on ${new Date().toLocaleDateString()}

const CONFIG = {
    // WhatsApp Business Number (include country code, no + sign)
    WHATSAPP_NUMBER: '${config.whatsapp}',
    
    // Business Information
    BUSINESS: {
        name: '${config.businessName}',
        email: '${config.email}',
        phone: '${config.phone}',
        address: '${config.address}',
        
        // Social Media Links
        social: {
            whatsapp: 'https://wa.me/${config.whatsapp}',
            instagram: 'https://instagram.com/${config.instagram}',
            tiktok: 'https://tiktok.com/@${config.tiktok}',
            email: 'mailto:${config.email}'
        }
    },
    
    // Default WhatsApp Messages
    MESSAGES: {
        general: 'Hi! I\\'m interested in your printing services',
        dtf: 'Hi! I\\'m interested in DTF printing services',
        embroidery: 'Hi! I\\'m interested in embroidery services',
        tags: 'Hi! I\\'m interested in tags and stickers',
        custom: 'Hi! I\\'m interested in custom printing services'
    },
    
    // Website Settings
    SETTINGS: {
        enableTypingEffect: true,
        enableParallax: true,
        enableAnimations: true,
        animationDuration: 1000
    }
};

// Helper function to generate WhatsApp URL
function getWhatsAppURL(message = CONFIG.MESSAGES.general) {
    return \`https://wa.me/\${CONFIG.WHATSAPP_NUMBER}?text=\${encodeURIComponent(message)}\`;
}

// Helper function to update WhatsApp links
function updateWhatsAppLinks() {
    const whatsappLinks = document.querySelectorAll('a[href*="YOUR_WHATSAPP_NUMBER"]');
    whatsappLinks.forEach(link => {
        const href = link.getAttribute('href');
        const updatedHref = href.replace('YOUR_WHATSAPP_NUMBER', CONFIG.WHATSAPP_NUMBER);
        link.setAttribute('href', updatedHref);
    });
}

// Initialize configuration when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (CONFIG.WHATSAPP_NUMBER !== 'YOUR_WHATSAPP_NUMBER') {
        updateWhatsAppLinks();
    } else {
        console.warn('⚠️ Please update your WhatsApp number in config.js');
    }
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}`;
            
            document.getElementById('config-output').value = configCode;
            document.getElementById('success-message').style.display = 'block';
            document.getElementById('generated-config').style.display = 'block';
            
            // Scroll to the generated config
            document.getElementById('generated-config').scrollIntoView({ behavior: 'smooth' });
        });
        
        function copyConfig() {
            const configOutput = document.getElementById('config-output');
            configOutput.select();
            configOutput.setSelectionRange(0, 99999);
            document.execCommand('copy');
            
            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = '✅ Copied!';
            btn.style.background = '#10B981';
            
            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = '#FF6B35';
            }, 2000);
        }
    </script>
</body>
</html>
